#!/usr/bin/env python3
"""
Test script to verify EvalSha site replication functionality.
This script tests that EvalSha operations are properly replicated to site nodes.
"""

import grpc
import time
import sys
import os

# Add the proto directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'proto'))

import rustycluster_pb2
import rustycluster_pb2_grpc

def create_channel(address):
    """Create a gRPC channel with appropriate settings."""
    return grpc.insecure_channel(address)

def authenticate(stub, username, password):
    """Authenticate and get session token."""
    try:
        auth_request = rustycluster_pb2.AuthRequest(
            username=username,
            password=password
        )
        response = stub.Authenticate(auth_request)
        if response.success:
            print(f"✅ Authentication successful. Token: {response.token[:20]}...")
            return response.token
        else:
            print(f"❌ Authentication failed: {response.message}")
            return None
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return None

def test_evalsha_site_replication():
    """Test EvalSha site replication functionality."""
    
    # Configuration
    primary_site = "127.0.0.1:50051"  # Primary site
    target_site = "127.0.0.1:50053"   # Target site (where replication should happen)
    username = "testuser"
    password = "testpass"
    
    print("🚀 Testing EvalSha Site Replication")
    print(f"Primary site: {primary_site}")
    print(f"Target site: {target_site}")
    print()
    
    # Connect to primary site
    try:
        primary_channel = create_channel(primary_site)
        primary_stub = rustycluster_pb2_grpc.KeyValueServiceStub(primary_channel)
        print(f"✅ Connected to primary site: {primary_site}")
    except Exception as e:
        print(f"❌ Failed to connect to primary site: {e}")
        return False
    
    # Connect to target site
    try:
        target_channel = create_channel(target_site)
        target_stub = rustycluster_pb2_grpc.KeyValueServiceStub(target_channel)
        print(f"✅ Connected to target site: {target_site}")
    except Exception as e:
        print(f"❌ Failed to connect to target site: {e}")
        return False
    
    # Authenticate with primary site
    primary_token = authenticate(primary_stub, username, password)
    if not primary_token:
        return False
    
    # Authenticate with target site
    target_token = authenticate(target_stub, username, password)
    if not target_token:
        return False
    
    # Create metadata for authentication
    primary_metadata = [('authorization', f'Bearer {primary_token}')]
    target_metadata = [('authorization', f'Bearer {target_token}')]
    
    print("\n📝 Step 1: Load script on primary site")
    
    # Load a simple Lua script that performs HSET operations
    script = """
local hashKey = KEYS[1]
for i = 1, #ARGV, 2 do
    redis.call('hset', hashKey, ARGV[i], ARGV[i + 1])
end
return "OK"
"""
    
    try:
        load_request = rustycluster_pb2.LoadScriptRequest(script=script)
        response = primary_stub.LoadScript(load_request, metadata=primary_metadata)
        if response.success:
            sha = response.sha
            print(f"✅ Script loaded successfully. SHA: {sha}")
        else:
            print("❌ Failed to load script")
            return False
    except Exception as e:
        print(f"❌ Error loading script: {e}")
        return False
    
    print("\n📝 Step 2: Execute EvalSha on primary site (should trigger site replication)")
    
    # Execute the script with some test data
    test_key = f"test_hash_{int(time.time())}"
    test_fields = ["field1", "value1", "field2", "value2", "field3", "value3"]
    
    try:
        evalsha_request = rustycluster_pb2.EvalShaRequest(
            sha=sha,
            keys=[test_key],
            args=test_fields,
            skip_replication=False,  # Allow local replication
            skip_site_replication=False  # Allow site replication
        )
        response = primary_stub.EvalSha(evalsha_request, metadata=primary_metadata)
        if response.success:
            print(f"✅ EvalSha executed successfully on primary site. Result: {response.result}")
        else:
            print("❌ EvalSha failed on primary site")
            return False
    except Exception as e:
        print(f"❌ Error executing EvalSha: {e}")
        return False
    
    print("\n⏳ Step 3: Wait for site replication to complete...")
    time.sleep(3)  # Wait for site replication
    
    print("\n🔍 Step 4: Verify data exists on target site")
    
    # Check if the hash was replicated to the target site
    try:
        # First, load the script on target site (it should have been replicated)
        load_request = rustycluster_pb2.LoadScriptRequest(script=script)
        response = target_stub.LoadScript(load_request, metadata=target_metadata)
        print(f"✅ Script available on target site. SHA: {response.sha}")
        
        # Check if the hash data exists
        hgetall_request = rustycluster_pb2.HGetAllRequest(key=test_key)
        response = target_stub.HGetAll(hgetall_request, metadata=target_metadata)
        
        if response.fields:
            print(f"✅ Hash data found on target site:")
            for field, value in response.fields.items():
                print(f"   {field}: {value}")
            
            # Verify the data matches what we set
            expected_data = {"field1": "value1", "field2": "value2", "field3": "value3"}
            if response.fields == expected_data:
                print("✅ Data matches expected values!")
                return True
            else:
                print("❌ Data doesn't match expected values")
                print(f"Expected: {expected_data}")
                print(f"Actual: {dict(response.fields)}")
                return False
        else:
            print("❌ No hash data found on target site - site replication may have failed")
            return False
            
    except Exception as e:
        print(f"❌ Error verifying data on target site: {e}")
        return False

def main():
    """Main function."""
    print("=" * 60)
    print("EvalSha Site Replication Test")
    print("=" * 60)
    
    success = test_evalsha_site_replication()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 EvalSha site replication test PASSED!")
        print("✅ EvalSha operations are being replicated to site nodes correctly")
    else:
        print("❌ EvalSha site replication test FAILED!")
        print("⚠️  Check the server logs for more details")
    print("=" * 60)
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
