#!/usr/bin/env python3
"""
Quick test to verify BatchWrite optimization for site replication.
This script tests the performance improvement from using BatchWrite.
"""

import grpc
import asyncio
import time
import sys
import random
import string
import statistics

try:
    import rustycluster_pb2
    import rustycluster_pb2_grpc
except ImportError:
    print("Error: gRPC Python stubs not found.")
    print("Please generate them first with:")
    print("python -m grpc_tools.protoc -I. --python_out=. --grpc_python_out=. rustycluster.proto")
    sys.exit(1)

class BatchOptimizationTester:
    def __init__(self, host="localhost", port=50051, username="testuser", password="testpass"):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.session_token = None
        
    async def authenticate(self):
        """Authenticate and get session token"""
        if not self.username or not self.password:
            return True
            
        channel = grpc.aio.insecure_channel(f'{self.host}:{self.port}')
        stub = rustycluster_pb2_grpc.KeyValueServiceStub(channel)
        
        auth_request = rustycluster_pb2.AuthenticateRequest(
            username=self.username,
            password=self.password
        )
        
        try:
            response = await stub.Authenticate(auth_request)
            if response.success:
                self.session_token = response.session_token
                print(f"✓ Authentication successful")
                await channel.close()
                return True
            else:
                print(f"✗ Authentication failed: {response.message}")
                await channel.close()
                return False
        except Exception as e:
            print(f"✗ Authentication error: {e}")
            await channel.close()
            return False
    
    async def test_batch_performance(self, operations=2000, concurrency=50):
        """Test BatchWrite optimization performance"""
        print(f"\n🚀 Testing BatchWrite Optimization")
        print(f"Operations: {operations}, Concurrency: {concurrency}")
        print("=" * 50)
        
        if not await self.authenticate():
            return False
        
        # Create connections
        channels = []
        stubs = []
        for i in range(concurrency):
            channel = grpc.aio.insecure_channel(f'{self.host}:{self.port}')
            stub = rustycluster_pb2_grpc.KeyValueServiceStub(channel)
            channels.append(channel)
            stubs.append(stub)
        
        # Prepare metadata
        metadata = []
        if self.session_token:
            metadata = [('authorization', f'Bearer {self.session_token}')]
        
        # Generate test data
        test_data = []
        for i in range(operations):
            key = f"batch_test_{i}_{random.randint(1000, 9999)}"
            value = f"batch_value_{i}_{''.join(random.choices(string.ascii_letters, k=30))}"
            test_data.append((key, value))
        
        print(f"Generated {len(test_data)} test operations")
        
        # Create semaphore for concurrency control
        semaphore = asyncio.Semaphore(concurrency)
        
        # Run test
        start_time = time.time()
        
        async def single_operation(stub, key, value):
            async with semaphore:
                op_start = time.time()
                try:
                    request = rustycluster_pb2.SetRequest(
                        key=key,
                        value=value,
                        skip_replication=False,  # Enable local replication
                        skip_site_replication=False  # Enable site replication (BatchWrite optimization)
                    )
                    response = await stub.Set(request, metadata=metadata, timeout=10.0)
                    op_end = time.time()
                    return {
                        'success': response.success,
                        'latency': (op_end - op_start) * 1000,
                        'error': None
                    }
                except Exception as e:
                    op_end = time.time()
                    return {
                        'success': False,
                        'latency': (op_end - op_start) * 1000,
                        'error': str(e)
                    }
        
        # Execute operations
        print("Executing operations with BatchWrite optimization...")
        tasks = []
        for i, (key, value) in enumerate(test_data):
            stub = stubs[i % len(stubs)]
            task = single_operation(stub, key, value)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        # Analyze results
        duration = end_time - start_time
        successful = sum(1 for r in results if isinstance(r, dict) and r.get('success', False))
        failed = len(results) - successful
        
        latencies = [r['latency'] for r in results if isinstance(r, dict) and 'latency' in r]
        
        rps = successful / duration if duration > 0 else 0
        
        print(f"\n📊 BatchWrite Optimization Results:")
        print(f"Duration: {duration:.2f}s")
        print(f"Successful Operations: {successful:,}")
        print(f"Failed Operations: {failed:,}")
        print(f"Achieved RPS: {rps:.2f}")
        
        if latencies:
            avg_latency = statistics.mean(latencies)
            p95_latency = sorted(latencies)[int(len(latencies)*0.95)]
            p99_latency = sorted(latencies)[int(len(latencies)*0.99)]
            
            print(f"Average Latency: {avg_latency:.2f}ms")
            print(f"P95 Latency: {p95_latency:.2f}ms")
            print(f"P99 Latency: {p99_latency:.2f}ms")
        
        # Performance assessment
        print(f"\n🎯 Performance Assessment:")
        if rps >= 8000:
            print(f"🎉 EXCELLENT: {rps:.0f} RPS - BatchWrite optimization is working!")
        elif rps >= 7000:
            print(f"✅ GOOD: {rps:.0f} RPS - Significant improvement over baseline")
        elif rps >= 6000:
            print(f"✅ BASELINE: {rps:.0f} RPS - Back to original performance")
        else:
            print(f"⚠️ BELOW BASELINE: {rps:.0f} RPS - Need to investigate")
        
        # Close connections
        for channel in channels:
            await channel.close()
        
        return rps
    
    async def test_progressive_load(self):
        """Test with progressive load to find optimal performance"""
        print(f"\n🔍 Progressive Load Testing")
        
        test_configs = [
            {"ops": 1000, "conc": 25, "name": "Light Load"},
            {"ops": 2000, "conc": 50, "name": "Medium Load"},
            {"ops": 3000, "conc": 75, "name": "Heavy Load"},
            {"ops": 4000, "conc": 100, "name": "Max Load"},
        ]
        
        results = {}
        
        for config in test_configs:
            print(f"\n--- {config['name']} Test ---")
            rps = await self.test_batch_performance(config['ops'], config['conc'])
            results[config['name']] = rps
            
            # Wait between tests
            await asyncio.sleep(3)
        
        print(f"\n📈 Progressive Load Results:")
        print("=" * 40)
        for name, rps in results.items():
            print(f"{name:12}: {rps:7.0f} RPS")
        
        # Find best performance
        best_rps = max(results.values())
        best_config = [name for name, rps in results.items() if rps == best_rps][0]
        
        print(f"\n🏆 Best Performance: {best_rps:.0f} RPS ({best_config})")
        
        if best_rps >= 8000:
            print("🎉 SUCCESS: BatchWrite optimization achieved target performance!")
        elif best_rps >= 7000:
            print("✅ GOOD: Significant improvement, close to target")
        else:
            print("⚠️ NEEDS MORE WORK: Still below target performance")
        
        return results

async def main():
    tester = BatchOptimizationTester()
    
    print("🔧 BatchWrite Optimization Tester")
    print("Testing site replication performance with BatchWrite")
    print("=" * 60)
    
    # Run progressive load test
    results = await tester.test_progressive_load()
    
    print("\n💡 Analysis:")
    print("- BatchWrite reduces network round trips from N to 1")
    print("- Should see significant improvement in site replication performance")
    print("- Target: 8000+ RPS (33% improvement over 6000 RPS baseline)")
    
    # Determine success
    best_rps = max(results.values()) if results else 0
    if best_rps >= 7000:
        print("\n✅ OPTIMIZATION SUCCESSFUL!")
        sys.exit(0)
    else:
        print("\n❌ OPTIMIZATION NEEDS MORE WORK")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
